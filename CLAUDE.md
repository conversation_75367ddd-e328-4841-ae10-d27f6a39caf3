# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目结构

这是一个独立的静态HTML工具集合。每个根目录都代表一个完全独立的应用程序，没有相互依赖：

- `form_validator/`: 表单验证规则配置器 - 基于 async-validator 的可视化表单验证规则配置工具
- `workflow_node/`: 零代码工作流节点配置 - 支持数据操作、页面交互和表单处理的可视化工作流节点配置工具

## 架构设计

### 技术栈
- **前端**: 纯 HTML5、CSS3 和原生 JavaScript (ES6+)
- **无构建系统**: 直接文件服务，无需编译
- **外部依赖**: async-validator（仅 form_validator 模块使用）
- **语言**: 中文 (zh-CN) 界面文本

### 文件组织模式
每个模块遵循一致的结构：
```
模块名/
├── index.html          # 入口点和主界面
├── styles.css          # 所有样式
├── js/
│   ├── main.js         # 核心初始化和全局状态
│   ├── ui.js           # UI交互处理器 (form_validator)
│   ├── validation.js   # 验证逻辑 (form_validator)  
│   ├── node-config.js  # 配置生成器 (workflow_node)
│   ├── form-data.js    # 数据管理 (workflow_node)
│   ├── expression-helper.js # 表达式工具 (workflow_node)
│   └── ui-interaction.js    # UI处理器 (workflow_node)
└── async-validator.md  # 文档 (仅 form_validator)
```

## 开发指南

### 运行应用
无需构建过程，只需：
1. 在浏览器中直接打开相应的 `index.html` 文件，或
2. 使用任意HTTP服务器提供目录服务（如 `python -m http.server`）

### 代码约定
- **模块化 JavaScript**: 功能按职责分离到多个JS文件中
- **中文注释和变量**: 大多数代码注释和部分变量名使用中文
- **事件驱动架构**: 大量使用 onclick 处理器和 DOM 操作
- **全局状态管理**: 每个模块维护全局状态对象（如 `configData`、`currentNode`）

### 关键架构模式

#### form_validator 模块：
- **数据模型**: `configData` 对象存储字段配置和验证规则
- **UI组件**: 基于模态框的字段和规则编辑
- **验证**: 基于 async-validator 库模式构建
- **导出**: JSON 配置输出

#### workflow_node 模块：
- **节点系统**: 可配置的节点类型（数据操作、页面操作、组件）
- **动态UI**: 基于节点类型生成配置面板
- **表达式系统**: 支持变量表达式和条件逻辑
- **分类**: 数据操作、页面操作和组件操作

## 重要说明

- 每个目录完全独立 - 一个模块的变更不会影响其他模块
- 未配置包管理、测试框架或构建工具
- 应用程序设计为直接浏览器使用，无需编译
- 两个模块都是生产就绪的静态应用，无需服务器端处理