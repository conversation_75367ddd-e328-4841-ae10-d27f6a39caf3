// 主要页面逻辑和初始化代码

// 全局变量
let currentNode = null;
let isDrawerOpen = false;

// 切换节点组展开/收起
function toggleGroup(header) {
    const content = header.nextElementSibling;
    const icon = header.querySelector('.node-group-icon');
    
    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        icon.classList.remove('expanded');
    } else {
        content.classList.add('expanded');
        icon.classList.add('expanded');
    }
}

// 抽屉控制函数
function openDrawer() {
    const drawer = document.getElementById('config-drawer');
    const overlay = document.getElementById('drawer-overlay');
    
    drawer.classList.add('show');
    overlay.classList.add('show');
    isDrawerOpen = true;
    
    // 禁用页面滚动
    document.body.style.overflow = 'hidden';
}

function closeDrawer() {
    const drawer = document.getElementById('config-drawer');
    const overlay = document.getElementById('drawer-overlay');
    
    drawer.classList.remove('show');
    overlay.classList.remove('show');
    isDrawerOpen = false;
    
    // 恢复页面滚动
    document.body.style.overflow = '';
}

function toggleDrawer() {
    if (isDrawerOpen) {
        closeDrawer();
    } else {
        openDrawer();
    }
}

// 选择节点
function selectNode(nodeType) {
    // 移除之前的选中状态
    document.querySelectorAll('.node-card.active').forEach(item => {
        item.classList.remove('active');
    });
    
    // 添加选中状态
    document.querySelector(`[data-node="${nodeType}"]`).classList.add('active');
    
    currentNode = nodeType;
    showNodeConfig(nodeType);
}

// 显示节点配置
function showNodeConfig(nodeType) {
    const emptyState = document.getElementById('empty-state');
    const nodeConfigContent = document.getElementById('node-config-content');
    
    emptyState.style.display = 'none';
    
    // 使用通用配置生成器
    const configHTML = generateNodeConfig(nodeType);
    nodeConfigContent.innerHTML = configHTML;
    
    // 更新配置面板标题
    const header = document.querySelector('.config-panel-header span');
    const nodeConfig = nodeConfigs[nodeType];
    header.textContent = nodeConfig ? `${nodeConfig.title}` : `${nodeTypes[nodeType]?.name || '未知'}`;
    
    // 自动打开抽屉
    openDrawer();
    
    // 如果是页面组件相关节点，初始化表单选择
    if (nodeType === 'input-set-value') {
        // 延迟执行，确保DOM已经生成
        setTimeout(() => {
            const formSelect = document.getElementById('target-form-select');
            if (formSelect) {
                // 确保选中个人简历表单并触发字段更新
                formSelect.value = 'resume-form';
                onTargetFormSelectChange();
            }
        }, 0);
    } else if (nodeType === 'input-set-property') {
        // 初始化属性配置
        setTimeout(() => {
            loadPropertyPageComponentFields();
        }, 0);
    } else if (nodeType === 'input-visibility') {
        // 初始化可见性配置
        setTimeout(() => {
            loadVisibilityPageComponentFields();
        }, 0);
    }
    
    // 如果是数据操作节点，初始化输出变量
    if (nodeType.startsWith('data-')) {
        setTimeout(() => {
            // 初始化变量输入框的验证
            const variableInputs = document.querySelectorAll('.variable-input');
            variableInputs.forEach(input => {
                validateVariableName(input);
            });
            
            // 对于更新和删除节点，初始化条件验证
            if (nodeType === 'data-update' || nodeType === 'data-delete') {
                validateFilterConditions();
            }
        }, 0);
    }
}

// 页面加载完成后初始化
window.onload = function() {
    // 初始化搜索功能
    initializeSearch();
    
    // 添加键盘事件监听
    document.addEventListener('keydown', function(event) {
        // ESC键关闭抽屉
        if (event.key === 'Escape' && isDrawerOpen) {
            closeDrawer();
        }
    });
};

// 初始化搜索功能
function initializeSearch() {
    const searchInput = document.getElementById('node-search');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            filterNodes(e.target.value);
        });
    }
}

// 搜索过滤节点
function filterNodes(searchTerm) {
    const categorySection = document.querySelectorAll('.category-section');
    const nodeCards = document.querySelectorAll('.node-card');
    
    if (!searchTerm.trim()) {
        // 显示所有节点和分类
        categorySection.forEach(section => section.style.display = 'block');
        nodeCards.forEach(card => card.style.display = 'flex');
        return;
    }
    
    const term = searchTerm.toLowerCase();
    
    nodeCards.forEach(card => {
        const text = card.querySelector('.node-text').textContent.toLowerCase();
        const isMatch = text.includes(term);
        card.style.display = isMatch ? 'flex' : 'none';
    });
    
    // 隐藏没有可见节点的分类
    categorySection.forEach(section => {
        const visibleCards = section.querySelectorAll('.node-card[style="display: flex"], .node-card:not([style*="display: none"])');
        const hasVisibleCards = Array.from(visibleCards).some(card => 
            !card.style.display || card.style.display === 'flex'
        );
        section.style.display = hasVisibleCards ? 'block' : 'none';
    });
}