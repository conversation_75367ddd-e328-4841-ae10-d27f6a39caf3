// 常用字段工厂
const commonFields = {
    name: (label = '姓名', required = true) => ({
        name: 'name', label, type: 'text', required, placeholder: `请输入${label}`
    }),
    email: (label = '邮箱', required = true) => ({
        name: 'email', label, type: 'email', required, placeholder: `请输入${label}`
    }),
    phone: (label = '电话', required = true) => ({
        name: 'phone', label, type: 'text', required, placeholder: `请输入${label}`
    }),
    date: (name, label, required = true) => ({
        name, label, type: 'date', required
    }),
    select: (name, label, options, required = true) => ({
        name, label, type: 'select', required, options
    }),
    text: (name, label, required = false, placeholder = null) => ({
        name, label, type: 'text', required, placeholder: placeholder || `请输入${label}`
    }),
    number: (name, label, required = true, min = null, max = null, placeholder = null) => ({
        name, label, type: 'number', required, min, max, placeholder: placeholder || `请输入${label}`
    }),
    textarea: (name, label, required = false, placeholder = null) => ({
        name, label, type: 'textarea', required, placeholder: placeholder || `请输入${label}`
    }),
    array: (name, label, itemSchema, required = false) => ({
        name, label, type: 'array', required, itemSchema
    }),
    relatedForm: (name, label, relatedFormId, required = false) => ({
        name, label, type: 'related', required, relatedFormId
    })
};

// 部门选项
const departments = ['技术部', '市场部', '人事部', '财务部', '运营部'];

// 表单字段定义数据
const formFieldsData = {
    'user-form': [
        commonFields.name(),
        commonFields.email(),
        commonFields.phone(),
        commonFields.select('gender', '性别', ['男', '女']),
        commonFields.date('birth_date', '出生日期', false),
        commonFields.text('address', '地址'),
        commonFields.text('id_card', '身份证号')
    ],
    'resume-form': [
        commonFields.relatedForm('user_info', '用户信息', 'user-form', true),
        commonFields.select('education', '学历', ['高中', '专科', '本科', '硕士', '博士']),
        commonFields.text('school', '毕业院校'),
        commonFields.text('major', '专业'),
        commonFields.number('work_experience', '工作经验(年)', true, null, null, '请输入工作年限'),
        commonFields.array('work_history', '工作经历', {
            company: { label: '公司名称', type: 'text', required: true, placeholder: '请输入公司名称' },
            position: { label: '职位', type: 'text', required: true, placeholder: '请输入职位' },
            start_date: { label: '开始时间', type: 'date', required: true },
            end_date: { label: '结束时间', type: 'date', required: false },
            is_current: { label: '目前在职', type: 'checkbox', required: false },
            description: { label: '工作描述', type: 'textarea', required: false, placeholder: '请描述主要工作内容和成就' }
        }),
        commonFields.textarea('skills', '技能特长'),
        commonFields.textarea('self_introduction', '自我介绍')
    ],
};