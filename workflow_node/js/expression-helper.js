// {{}}表达式相关功能
const ExpressionHelper = {
    // 支持的变量上下文
    contexts: {
        form: {
            name: '表单数据',
            description: '当前表单的字段数据',
            fields: [] // 将动态填充
        },
        query: {
            name: '查询参数',
            description: 'URL查询参数或上一节点传递的参数',
            fields: ['id', 'page', 'size', 'keyword']
        },
        result: {
            name: '执行结果',
            description: '上一节点的执行结果',
            fields: ['data', 'count', 'success', 'error', 'affectedRows']
        },
        user: {
            name: '用户信息',
            description: '当前登录用户的信息',
            fields: ['id', 'name', 'email', 'role', 'department']
        },
        system: {
            name: '系统变量',
            description: '系统提供的变量',
            fields: ['currentTime', 'currentDate', 'random', 'uuid']
        }
    },
    
    // 初始化表单字段
    initFormFields(formKey) {
        if (formKey && formFieldsData[formKey]) {
            const fieldNames = [];
            formFieldsData[formKey].forEach(field => {
                // 添加普通字段
                fieldNames.push(field.name);
                
                // 如果是子表单字段，添加其子字段
                if (field.type === 'array' && field.itemSchema) {
                    Object.keys(field.itemSchema).forEach(subFieldKey => {
                        fieldNames.push(`${field.name}.${subFieldKey}`);
                    });
                }
            });
            this.contexts.form.fields = fieldNames;
        }
    },
    
    // 解析表达式
    parseExpression(expression) {
        if (!expression || typeof expression !== 'string') {
            return { valid: false, error: '表达式不能为空' };
        }
        
        const bracesRegex = /\{\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}\}/g;
        const matches = expression.match(bracesRegex);
        
        if (!matches) {
            return { valid: true, variables: [] };
        }
        
        const variables = [];
        const errors = [];
        
        matches.forEach(match => {
            const variablePath = match.substring(2, match.length - 2); // 去掉{{}}包装
            const parts = variablePath.split('.');
            
            if (parts.length === 0) {
                errors.push(`无效的变量格式: ${match}`);
                return;
            }
            
            const context = parts[0];
            if (!this.contexts[context]) {
                errors.push(`未知的上下文: ${context}`);
                return;
            }
            
            if (parts.length > 1) {
                const field = parts[1];
                const validFields = this.contexts[context].fields;
                if (validFields.length > 0 && !validFields.includes(field)) {
                    errors.push(`上下文 ${context} 中没有字段: ${field}`);
                    return;
                }
            }
            
            variables.push({
                match: match,
                context: context,
                field: parts[1] || null,
                path: variablePath
            });
        });
        
        return {
            valid: errors.length === 0,
            variables: variables,
            errors: errors
        };
    },
    
    // 验证表达式
    validateExpression(expression) {
        const parseResult = this.parseExpression(expression);
        
        if (!parseResult.valid) {
            return {
                valid: false,
                errors: parseResult.errors,
                suggestions: this.getSuggestions(expression)
            };
        }
        
        // 检查JavaScript语法
        try {
            // 创建一个测试函数来验证语法
            const testExpression = expression.replace(
                /\{\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}\}/g,
                'context.$1'
            );
            
            // 简单的语法检查，不执行
            new Function('context', `return ${testExpression}`);
            
            return {
                valid: true,
                variables: parseResult.variables,
                message: '表达式语法正确'
            };
        } catch (e) {
            return {
                valid: false,
                errors: [`JavaScript语法错误: ${e.message}`],
                suggestions: this.getSuggestions(expression)
            };
        }
    },
    
    // 获取建议
    getSuggestions(expression) {
        const suggestions = [];
        
        // 添加上下文建议
        Object.keys(this.contexts).forEach(context => {
            suggestions.push({
                type: 'context',
                text: `{{${context}}}`,
                description: this.contexts[context].description
            });
        });
        
        // 如果表达式包含{{前缀，尝试提供字段建议
        const bracesMatch = expression.match(/\{\{([a-zA-Z_][a-zA-Z0-9_]*)$/);
        if (bracesMatch) {
            const context = bracesMatch[1];
            if (this.contexts[context]) {
                this.contexts[context].fields.forEach(field => {
                    suggestions.push({
                        type: 'field',
                        text: `{{${context}.${field}}}`,
                        description: `${context}中的${field}字段`
                    });
                });
            }
        }
        
        return suggestions;
    },
    
    // 获取表达式预览
    getExpressionPreview(expression, contextData = {}) {
        if (!expression) return '';
        
        try {
            // 替换{{}}变量为实际值
            let preview = expression;
            
            // 替换系统变量
            preview = preview.replace(/\{\{system\.currentTime\}\}/g, new Date().toLocaleString());
            preview = preview.replace(/\{\{system\.currentDate\}\}/g, new Date().toLocaleDateString());
            preview = preview.replace(/\{\{system\.random\}\}/g, Math.random().toString(36).substr(2, 9));
            preview = preview.replace(/\{\{system\.uuid\}\}/g, 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            }));
            
            // 替换其他变量（使用示例数据）
            Object.keys(contextData).forEach(context => {
                const regex = new RegExp(`\\{\\{${context}\\.([a-zA-Z_][a-zA-Z0-9_]*)\\}\\}`, 'g');
                preview = preview.replace(regex, (match, field) => {
                    return contextData[context][field] || `[${context}.${field}]`;
                });
            });
            
            return preview;
        } catch (e) {
            return '预览生成失败';
        }
    }
};

// 获取用户自定义的输出变量名称
function getCustomOutputVariables() {
    const variableInputs = document.querySelectorAll('.variable-input');
    const customVariables = {};
    
    variableInputs.forEach(input => {
        const originalName = input.getAttribute('data-original-name');
        const variableName = input.value.trim();
        
        if (variableName) {
            customVariables[originalName] = variableName;
        }
    });
    
    return customVariables;
}

// 更新表达式助手中的result字段
ExpressionHelper.updateResultFields = function() {
    const customVariables = getCustomOutputVariables();
    
    // 更新result上下文字段
    this.contexts.result.fields = Object.values(customVariables);
    
    // 如果没有自定义变量，使用默认字段
    if (this.contexts.result.fields.length === 0) {
        this.contexts.result.fields = ['data', 'count', 'success', 'error', 'affectedRows'];
    }
};

// 获取完整的变量路径（包含自定义前缀）
ExpressionHelper.getVariablePath = function(originalName) {
    const customVariables = getCustomOutputVariables();
    
    if (customVariables[originalName]) {
        return `result.${customVariables[originalName]}`;
    }
    
    // 默认映射
    const defaultMapping = {
        '记录ID': 'result.id',
        '成功状态': 'result.success',
        '错误信息': 'result.error',
        '影响行数': 'result.affectedRows',
        '数据列表': 'result.data',
        '总记录数': 'result.totalCount',
        '总页数': 'result.pageCount',
        '当前页码': 'result.currentPage'
    };
    
    return defaultMapping[originalName] || `result.${originalName}`;
};