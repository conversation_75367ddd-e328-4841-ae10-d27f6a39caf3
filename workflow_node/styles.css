:root {
    /* 主色调 */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --error-color: #e74c3c;
    --info-color: #17a2b8;
    
    /* 背景色 */
    --bg-primary: #f5f5f5;
    --bg-white: white;
    --bg-light: #f8f9fa;
    --bg-muted: #ecf0f1;
    --bg-dark: #34495e;
    
    /* 文字颜色 */
    --text-primary: #333;
    --text-secondary: #666;
    --text-muted: #6c757d;
    --text-dark: #2c3e50;
    --text-white: white;
    
    /* 边框颜色 */
    --border-light: #e9ecef;
    --border-default: #bdc3c7;
    --border-dark: #ddd;
    
    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 25px;
    
    /* 圆角 */
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 10px;
    --radius-round: 50%;
    
    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'Monaco', 'Consolas', monospace;
    --font-size-sm: 11px;
    --font-size-xs: 12px;
    --font-size-default: 13px;
    --font-size-md: 14px;
    --font-size-lg: 16px;
    --font-size-xl: 24px;
    
    /* 过渡 */
    --transition-fast: 0.2s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
    height: 100%;
}

.header {
    background: var(--bg-white);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.header h1 {
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-xl);
}

.header p {
    color: var(--text-secondary);
}

.main-content {
    min-height: calc(100vh - 140px);
    padding: var(--spacing-lg) 0;
}

.node-list {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

.node-list-header {
    background: var(--bg-dark);
    color: var(--text-white);
    padding: var(--spacing-md);
    font-weight: 600;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.node-list-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

.node-group {
    border-bottom: 1px solid var(--bg-muted);
}

.node-group:last-child {
    border-bottom: none;
}

.node-group-header {
    background: #ecf0f1;
    padding: 12px 15px;
    font-weight: 600;
    color: #2c3e50;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.node-group-header:hover {
    background: #dde1e6;
}

.node-group-icon {
    font-size: 12px;
    transition: transform 0.2s;
}

.node-group-icon.expanded {
    transform: rotate(90deg);
}

.node-group-content {
    display: none;
    padding: 0;
}

.node-group-content.expanded {
    display: block;
}

.node-item {
    padding: 12px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.2s;
    margin-left: 15px;
}

.node-item:hover {
    background: var(--bg-light);
}

.node-item.active {
    background: var(--primary-color);
    color: var(--text-white);
    border-radius: var(--radius-sm);
    margin: 2px var(--spacing-md);
    padding: 10px;
}

.node-item:last-child {
    border-bottom: none;
}

/* 抽屉遮罩层 */
.drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-fast), visibility var(--transition-fast);
}

.drawer-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 右侧抽屉配置面板 */
.config-drawer {
    position: fixed;
    top: 0;
    right: -500px;
    width: 500px;
    height: 100%;
    background: var(--bg-white);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 999;
    display: flex;
    flex-direction: column;
    transition: right var(--transition-fast);
    font-size: 12px;
}

.config-drawer.show {
    right: 0;
}

.config-panel-header {
    background: var(--success-color);
    color: var(--text-white);
    padding: var(--spacing-md);
    font-size: var(--font-size-lg);
    font-weight: bold;
    border-radius: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.drawer-close-btn {
    background: transparent;
    border: none;
    color: var(--text-white);
    font-size: 24px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-round);
    transition: background-color var(--transition-fast);
}

.drawer-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.config-panel-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    font-size: 12px;
}

/* 通用样式类 */
.text-muted {
    color: var(--text-secondary);
    font-size: 12px;
}

/* 表单统一样式 */
.form-control {
    width: 100%;
    padding: 4px 8px;
    border: 1px solid var(--border-default);
    border-radius: var(--radius-sm);
    font-size: 11px;
    height: 22px;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-control.textarea {
    height: 60px;
    resize: vertical;
    font-family: var(--font-mono);
    font-size: 10px;
    padding: 4px 8px;
}

.btn {
    padding: 3px 8px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all var(--transition-fast);
    height: 24px;
    background: var(--primary-color);
    color: var(--text-white);
}

.btn-small {
    min-width: auto;
    padding: 2px 6px;
    font-size: 12px;
    height: 22px;
}

.form-section {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--bg-muted);
    font-size: 12px;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-icon {
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: var(--radius-round);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: var(--font-size-xs);
}

.form-group {
    margin-bottom: 8px;
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 8px;
    align-items: start;
    min-height: 24px;
}

.form-group.full-width {
    grid-template-columns: 1fr;
}

.form-group.full-width label {
    margin-bottom: 4px;
    display: block;
}

.form-group label {
    margin: 0;
    font-weight: 500;
    color: var(--text-dark);
    font-size: 12px;
    white-space: nowrap;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 8px;
}

.form-row .form-group {
    margin-bottom: 0;
}


.empty-state {
    text-align: center;
    color: var(--text-secondary);
    padding: 20px 12px;
    font-size: 12px;
}

.empty-state h3 {
    color: var(--text-dark);
    margin-bottom: 8px;
    font-size: 14px;
}

.condition-builder {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    margin-top: 4px;
}

.condition-row {
    display: grid;
    grid-template-columns: 150px 140px 1fr 40px;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.condition-row:last-child {
    margin-bottom: 0;
}

.condition-connector {
    grid-column: span 4;
    text-align: center;
    margin: 10px 0;
    color: #666;
    font-weight: 500;
}

.condition-warning {
    grid-column: span 4;
    text-align: center;
    margin: 10px 0;
    padding: 8px;
    background-color: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 4px;
}

.warning-text {
    color: #e53e3e;
    font-weight: 500;
    font-size: 12px;
}

.field-select, .operator-select {
    padding: 6px 10px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.value-input-container {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: var(--spacing-sm);
    align-items: center;
}

.value-type-select {
    padding: var(--spacing-xs) 6px;
    border: 1px solid var(--border-default);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    background: var(--bg-white);
}

.value-input {
    padding: 6px 10px;
    border: 1px solid var(--border-default);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
}

.expression-input {
    font-family: var(--font-mono) !important;
    background-color: var(--bg-light);
}

/* 表达式验证和提示相关样式 */
.expression-validator {
    position: relative;
    margin-top: 4px;
}

.form-group:not(.full-width) .expression-validator {
    margin-top: 0;
}

/* 关联表单选择器样式 */
.related-form-selector {
    padding: 10px;
    border: 1px solid #e1e8ed;
    border-radius: 4px;
    background-color: #f8f9fa;
    margin-bottom: 8px;
}

.related-form-selector label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    font-size: 12px;
}

.related-form-selector select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    margin-bottom: 5px;
}

.related-form-selector small {
    display: block;
    margin-top: 5px;
}

.expression-validation {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.expression-validation.show {
    display: block;
}

.validation-message {
    padding: 8px 12px;
    font-size: 12px;
    border-bottom: 1px solid #eee;
}

.validation-message.success {
    color: #28a745;
    background-color: #d4edda;
}

.validation-message.error {
    color: #dc3545;
    background-color: #f8d7da;
}

.validation-message.warning {
    color: #856404;
    background-color: #fff3cd;
}

.expression-suggestions {
    padding: 0;
    margin: 0;
    list-style: none;
}

.suggestion-item {
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.suggestion-item:hover {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-type {
    font-weight: 600;
    color: #6c757d;
    margin-right: 5px;
}

.expression-preview {
    padding: 8px 12px;
    font-size: 11px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
    font-style: italic;
    color: #6c757d;
}


/* 自动完成相关样式 */
.autocomplete-popup {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1002;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.autocomplete-popup.show {
    display: block;
}

.autocomplete-item {
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.autocomplete-item:hover {
    background-color: #f8f9fa;
}

.autocomplete-item:last-child {
    border-bottom: none;
}

.autocomplete-item-text {
    flex: 1;
}

.autocomplete-item-desc {
    color: #6c757d;
    font-size: 11px;
    font-style: italic;
    margin-left: 8px;
}

.autocomplete-category {
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.autocomplete-highlight {
    font-weight: 600;
    color: #007bff;
}


/* 动态字段样式 */
.field-item {
    background: var(--bg-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    padding: var(--spacing-md);
    margin-bottom: 10px;
}

.field-item:last-child {
    margin-bottom: 0;
}

.field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.field-name {
    font-weight: 600;
    color: var(--text-dark);
}

.field-required {
    color: var(--error-color);
    font-size: var(--font-size-xs);
}

.field-value-container {
    display: grid;
    grid-template-columns: 100px 1fr;
    gap: 10px;
    align-items: center;
}

.value-type-selector {
    padding: 4px 8px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.field-value-input {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 13px;
}


.field-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
    font-style: italic;
}

/* Input/Output 参数样式 */
.param-section {
    background: var(--bg-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-bottom: 8px;
}

.param-header {
    background: var(--border-light);
    padding: 6px var(--spacing-md);
    border-bottom: 1px solid var(--border-dark);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
    font-weight: 600;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 13px;
}

.param-header.input {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.param-header.output {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.param-content {
    padding: 8px;
}

/* 参数行布局 */
.param-row {
    display: grid;
    grid-template-columns: 1fr 60px 2fr;
    gap: 8px;
    align-items: center;
    min-height: 28px;
}

.param-row:last-child {
    margin-bottom: 0;
}

/* 参数标题行样式 */
.param-header-row {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 6px 4px;
    margin-bottom: 4px;
    font-weight: 600;
    color: #495057;
    font-size: 11px;
}

/* 参数项行样式 */
.param-item-row {
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    padding: 4px;
    margin-bottom: 4px;
}

/* 参数单元格样式 */
.param-cell {
    display: flex;
    align-items: center;
}

.param-name-cell {
    justify-content: flex-start;
}

.param-type-cell {
    justify-content: center;
}

.param-value-cell {
    justify-content: stretch;
}

.param-name {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 12px;
}

.param-type {
    background: var(--text-muted);
    color: var(--text-white);
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.param-type.input {
    background: #28a745;
}

.param-type.output {
    background: var(--info-color);
}

.param-input {
    font-size: 12px;
    padding: 2px 4px;
    height: 24px;
    border: 1px solid var(--border-default);
    border-radius: 3px;
    width: 100%;
}

.param-input.readonly-param {
    background-color: #f8f9fa;
    color: #6c757d;
    cursor: default;
}

.param-input.readonly-param[value="表达式"] {
    font-style: italic;
}

/* 变量输入框样式 */
.variable-input-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.variable-input {
    font-family: var(--font-mono);
    background-color: var(--bg-light) !important;
}

.variable-prefix {
    position: absolute;
    left: 8px;
    color: var(--text-muted);
    font-family: var(--font-mono);
    font-size: 12px;
    pointer-events: none;
    z-index: 1;
}

.variable-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
    outline: none;
}

.variable-input.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.variable-input.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2);
}

.param-source {
    padding: 4px 8px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.param-input {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 13px;
}

.output-readonly {
    background: var(--bg-light);
    color: var(--text-muted);
}

/* 子表单字段样式 */
.array-field-container {
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-light);
    margin-bottom: 8px;
}

.array-field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: 8px;
    background: var(--bg-white);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-light);
    margin-bottom: 8px;
}

.array-field-header:hover {
    background: var(--bg-light);
}

.array-field-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.array-toggle-icon {
    font-size: 12px;
    transition: transform 0.2s;
}

.array-field-content {
    padding: 8px;
}

.array-items {
    margin-bottom: var(--spacing-md);
}

.array-item {
    background: var(--bg-white);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
}

.array-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.array-item-title {
    font-weight: 600;
    color: var(--text-dark);
}

.array-item-fields {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* 子表单项字段使用与普通字段一致的内联样式 */
.array-item-fields .field-item-inline {
    display: grid;
    grid-template-columns: 120px 50px 1fr;
    gap: 8px;
    align-items: center;
    padding: 4px 8px;
    margin-bottom: 4px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    min-height: 28px;
}

.array-item-fields .field-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.array-item-fields .field-label .required-mark {
    color: var(--error-color);
    margin-right: 2px;
}

.array-item-fields .field-type {
    background: var(--text-muted);
    color: var(--text-white);
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

/* 数组字段中的字段类型颜色继承全局规则 */
.array-item-fields .field-type-string,
.array-item-fields .field-type-text,
.array-item-fields .field-type-textarea {
    background: #3498db;
}

.array-item-fields .field-type-number,
.array-item-fields .field-type-integer,
.array-item-fields .field-type-float {
    background: #27ae60;
}

.array-item-fields .field-type-boolean,
.array-item-fields .field-type-checkbox {
    background: #9b59b6;
}

.array-item-fields .field-type-date,
.array-item-fields .field-type-time,
.array-item-fields .field-type-datetime {
    background: #f39c12;
}

.array-item-fields .field-type-email,
.array-item-fields .field-type-url,
.array-item-fields .field-type-phone,
.array-item-fields .field-type-idcard {
    background: #17a2b8;
}

.array-item-fields .field-type-select,
.array-item-fields .field-type-radio {
    background: #34495e;
}

.array-item-fields .field-type-array,
.array-item-fields .field-type-object,
.array-item-fields .field-type-related {
    background: #e74c3c;
}

.array-item-fields .field-value-input {
    font-size: 12px;
    padding: 2px 4px;
    height: 22px;
    border: 1px solid var(--border-default);
    border-radius: 3px;
    width: 100%;
}

/* 修复子表单字段中表达式验证器的样式 */
.array-item-fields .expression-validator {
    position: relative;
    width: 100%;
}

.array-item-fields .expression-validator input,
.array-item-fields .expression-validator textarea {
    width: 100%;
    font-size: 12px;
    padding: 2px 4px;
    height: 22px;
    border: 1px solid var(--border-default);
    border-radius: 3px;
    font-family: var(--font-mono);
    background-color: var(--bg-light);
}

.array-item-fields .expression-validator textarea {
    height: 40px;
    resize: vertical;
}

/* 保持向后兼容的样式 */
.array-item-field {
    display: flex;
    flex-direction: column;
}

.array-item-field label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.array-item-field .form-control {
    font-size: var(--font-size-xs);
    padding: 6px 8px;
}

.array-item-field:has(textarea) {
    grid-column: 1 / -1;
}

.array-item-field:has(input[type="checkbox"]) {
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-sm);
}

.array-item-field:has(input[type="checkbox"]) label {
    margin-bottom: 0;
}

/* 内联字段样式 */
.field-item-inline {
    display: grid;
    grid-template-columns: 1fr 50px 2fr;
    gap: 8px;
    align-items: center;
    padding: 4px 8px;
    margin-bottom: 4px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    min-height: 28px;
}

.field-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.field-label .required-mark {
    color: var(--error-color);
    margin-right: 2px;
}

.field-type {
    background: var(--text-muted);
    color: var(--text-white);
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

/* 基于字段类型的颜色规则 */
/* 文本类型 - 蓝色 */
.field-type-string,
.field-type-text,
.field-type-textarea {
    background: #3498db;
}

/* 数字类型 - 绿色 */
.field-type-number,
.field-type-integer,
.field-type-float {
    background: #27ae60;
}

/* 布尔类型 - 紫色 */
.field-type-boolean,
.field-type-checkbox {
    background: #9b59b6;
}

/* 日期时间类型 - 橙色 */
.field-type-date,
.field-type-time,
.field-type-datetime {
    background: #f39c12;
}

/* 特殊验证类型 - 青色 */
.field-type-email,
.field-type-url,
.field-type-phone,
.field-type-idcard {
    background: #17a2b8;
}

/* 选择类型 - 深蓝色 */
.field-type-select,
.field-type-radio {
    background: #34495e;
}

/* 复合类型 - 红色 */
.field-type-array,
.field-type-object,
.field-type-related {
    background: #e74c3c;
}

.field-input-inline {
    font-size: 12px;
    padding: 2px 4px;
    height: 22px;
    border: 1px solid var(--border-default);
    border-radius: 3px;
    width: 100%;
}

.array-items {
    margin-bottom: var(--spacing-md);
}

.array-item {
    background: var(--bg-white);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
}

.array-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.array-item-title {
    font-weight: 600;
    color: var(--text-dark);
}

.array-item-fields {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* 子表单字段中的表单组样式 */
.array-item-fields .form-group {
    margin-bottom: 8px;
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 8px;
    align-items: start;
    min-height: 24px;
}

.array-item-fields .form-group:last-child {
    margin-bottom: 0;
}

.array-item-fields .form-group label {
    margin: 0;
    font-weight: 500;
    color: var(--text-dark);
    font-size: 12px;
    white-space: nowrap;
}

/* 确保子表单字段中的表达式验证器正确显示 */
.array-item-fields .form-group .expression-validator {
    margin-top: 0;
}

/* 处理子表单字段中的textarea */
.array-item-fields .form-group:has(textarea) {
    grid-template-columns: 1fr;
}

.array-item-fields .form-group:has(textarea) label {
    margin-bottom: 4px;
    display: block;
}

/* 处理子表单字段中的checkbox */
.array-item-fields .form-group:has(input[type="checkbox"]) {
    grid-template-columns: 1fr;
    flex-direction: row;
    align-items: center;
    gap: 8px;
}

.array-item-fields .form-group:has(input[type="checkbox"]) label {
    margin-bottom: 0;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

.btn-outline-danger {
    background: transparent;
    color: var(--error-color);
    border: 1px solid var(--error-color);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 11px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-outline-danger:hover {
    background: var(--error-color);
    color: var(--text-white);
}

.btn-sm {
    font-size: 12px;
    padding: 2px 6px;
    height: 22px;
}

/* 搜索栏样式 */
.search-container {
    margin-bottom: var(--spacing-lg);
}

.search-box {
    position: relative;
    max-width: 400px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid var(--border-light);
    border-radius: 25px;
    font-size: var(--font-size-md);
    background: var(--bg-white);
    color: var(--text-primary);
    outline: none;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    color: var(--text-muted);
    pointer-events: none;
}

/* 节点网格布局 */
.node-grid {
    max-width: 1200px;
    margin: 0 auto;
}

/* 分类区域样式 */
.category-section {
    margin-bottom: var(--spacing-xl);
}

.category-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--border-light);
}

.category-header h3 {
    color: var(--text-dark);
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* 节点卡片网格 */
.node-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--spacing-md);
    padding: 0;
}

/* 节点卡片样式 */
.node-card {
    background: var(--bg-white);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    min-height: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
    overflow: hidden;
}

.node-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.node-card.active {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-dark);
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.node-card.active .node-text {
    color: var(--text-white);
    font-weight: 600;
}

/* 节点图标样式 */
.node-icon {
    font-size: 32px;
    margin-bottom: var(--spacing-xs);
    filter: grayscale(20%);
    transition: all var(--transition-fast);
}

.node-card:hover .node-icon {
    transform: scale(1.1);
    filter: grayscale(0%);
}

.node-card.active .node-icon {
    filter: brightness(1.2) drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    transform: scale(1.05);
}

/* 节点文字样式 */
.node-text {
    font-size: var(--font-size-md);
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.3;
    transition: all var(--transition-fast);
}

.node-card:hover .node-text {
    color: var(--primary-color);
    font-weight: 600;
}

/* 隐藏旧的节点列表样式 */
.node-list,
.node-group,
.node-group-header,
.node-group-content,
.node-item {
    display: none;
}

/* 媒体查询优化 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .node-cards {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: var(--spacing-sm);
    }
    
    .node-card {
        min-height: 100px;
        padding: var(--spacing-md);
    }
    
    .node-icon {
        font-size: 28px;
    }
    
    .node-text {
        font-size: var(--font-size-default);
    }
    
    .search-input {
        font-size: var(--font-size-default);
    }
    
    /* 移动端抽屉全屏显示 */
    .config-drawer {
        width: 100%;
        right: -100%;
    }
    
    .config-drawer.show {
        right: 0;
    }
}

/* 通用简洁 radio 按钮组样式 */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-top: 4px;
}

.radio-group.horizontal {
    flex-direction: row;
    flex-wrap: wrap;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.radio-option:hover {
    background: var(--bg-light);
    border-color: var(--primary-color);
}

.radio-option input[type="radio"] {
    margin: 0;
    width: 14px;
    height: 14px;
    accent-color: var(--primary-color);
    cursor: pointer;
    flex-shrink: 0;
}

.radio-option input[type="radio"]:checked + .radio-label {
    color: var(--primary-color);
    font-weight: 600;
}

.radio-option:has(input[type="radio"]:checked) {
    background: rgba(52, 152, 219, 0.05);
    border-color: var(--primary-color);
}

.radio-label {
    flex: 1;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-dark);
    white-space: nowrap;
}

/* 更新模式选择器样式 - 使用通用 radio 组样式 */
.update-mode-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-top: 4px;
}

.subform-mode-selector {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-top: 4px;
}

/* 保持向后兼容的样式类名 */
.update-mode-selector .radio-option,
.subform-mode-selector .radio-option {
    padding: 6px 12px;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
}

.update-mode-selector .radio-option:hover,
.subform-mode-selector .radio-option:hover {
    background: var(--bg-light);
    border-color: var(--primary-color);
}

.update-mode-selector .radio-option input[type="radio"],
.subform-mode-selector .radio-option input[type="radio"] {
    width: 14px;
    height: 14px;
}

.update-mode-selector .radio-label,
.subform-mode-selector .radio-label {
    font-size: 12px;
    color: var(--text-dark);
}

.update-mode-selector .radio-option input[type="radio"]:checked + .radio-label,
.subform-mode-selector .radio-option input[type="radio"]:checked + .radio-label {
    color: var(--primary-color);
    font-weight: 600;
}

.update-mode-selector .radio-option:has(input[type="radio"]:checked),
.subform-mode-selector .radio-option:has(input[type="radio"]:checked) {
    background: rgba(52, 152, 219, 0.05);
    border-color: var(--primary-color);
}

/* 保持向后兼容的旧样式类名 */
.mode-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.mode-option:hover {
    background: var(--bg-light);
    border-color: var(--primary-color);
}

.mode-option input[type="radio"] {
    margin: 0;
    width: 14px;
    height: 14px;
    accent-color: var(--primary-color);
    cursor: pointer;
    flex-shrink: 0;
}

.mode-option input[type="radio"]:checked + .mode-label {
    color: var(--primary-color);
    font-weight: 600;
}

.mode-option:has(input[type="radio"]:checked) {
    background: rgba(52, 152, 219, 0.05);
    border-color: var(--primary-color);
}

.mode-label {
    flex: 1;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-dark);
    white-space: nowrap;
}

.mode-title {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 12px;
}

.mode-description {
    color: var(--text-secondary);
    font-size: 11px;
    line-height: 1.4;
    margin-top: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .radio-group {
        padding: 6px;
        gap: 6px;
    }
    
    .radio-option {
        padding: 4px 8px;
        gap: 6px;
    }
    
    .radio-label {
        font-size: 11px;
    }
    
    .update-mode-selector,
    .subform-mode-selector {
        padding: 6px;
        gap: 6px;
    }
    
    .mode-option {
        padding: 4px 8px;
        gap: 6px;
    }
    
    .mode-title {
        font-size: 11px;
    }
    
    .mode-description {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .node-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .config-drawer {
        width: 100%;
        right: -100%;
    }
}

@media (max-width: 360px) {
    .node-cards {
        grid-template-columns: 1fr;
    }
}

/* 字段过滤器样式 */
.field-filter-container {
    background: var(--bg-light);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-top: 4px;
}

.field-filter-select-container {
    margin-bottom: var(--spacing-md);
}

.field-filter-select-container select {
    width: 100%;
    min-height: 120px;
    font-size: var(--font-size-default);
}

.field-filter-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    flex-wrap: wrap;
}

.field-filter-selected {
    border-top: 1px solid var(--border-light);
    padding-top: var(--spacing-md);
}

.field-filter-label {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-default);
}

.field-filter-selected-list {
    min-height: 24px;
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    align-items: center;
}

.field-filter-empty {
    color: var(--text-muted);
    font-style: italic;
    font-size: var(--font-size-sm);
}

.field-filter-tag {
    background: var(--primary-color);
    color: var(--text-white);
    padding: 2px var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    display: inline-flex;
    align-items: center;
    gap: 2px;
}

.field-filter-tag::before {
    content: '•';
    font-size: var(--font-size-xs);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .field-filter-actions {
        flex-direction: column;
    }
    
    .field-filter-actions .btn {
        width: 100%;
    }
}

/* 子表单编辑器样式 */
.subform-editor {
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    background: var(--bg-light);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.subform-items {
    margin-bottom: var(--spacing-md);
}

.subform-item {
    background: var(--bg-white);
    border: 1px solid var(--border-default);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    padding: var(--spacing-md);
}

.subform-item:last-child {
    margin-bottom: 0;
}

.subform-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
}

.subform-item-title {
    font-weight: 600;
    color: var(--text-dark);
}

.subform-item-fields {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* 子表单项字段使用与普通字段一致的内联样式 */
.subform-item-fields .field-item-inline {
    display: grid;
    grid-template-columns: 120px 50px 1fr;
    gap: 8px;
    align-items: center;
    padding: 4px 8px;
    margin-bottom: 4px;
    background-color: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-sm);
    min-height: 28px;
}

.subform-item-fields .field-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-dark);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.subform-item-fields .field-label .required-mark {
    color: var(--error-color);
    margin-right: 2px;
}

.subform-item-fields .field-type {
    background: var(--text-muted);
    color: var(--text-white);
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

/* 子表单字段中的字段类型颜色继承全局规则 */
.subform-item-fields .field-type-string,
.subform-item-fields .field-type-text,
.subform-item-fields .field-type-textarea {
    background: #3498db;
}

.subform-item-fields .field-type-number,
.subform-item-fields .field-type-integer,
.subform-item-fields .field-type-float {
    background: #27ae60;
}

.subform-item-fields .field-type-boolean,
.subform-item-fields .field-type-checkbox {
    background: #9b59b6;
}

.subform-item-fields .field-type-date,
.subform-item-fields .field-type-time,
.subform-item-fields .field-type-datetime {
    background: #f39c12;
}

.subform-item-fields .field-type-email,
.subform-item-fields .field-type-url,
.subform-item-fields .field-type-phone,
.subform-item-fields .field-type-idcard {
    background: #17a2b8;
}

.subform-item-fields .field-type-select,
.subform-item-fields .field-type-radio {
    background: #34495e;
}

.subform-item-fields .field-type-array,
.subform-item-fields .field-type-object,
.subform-item-fields .field-type-related {
    background: #e74c3c;
}

.subform-item-fields .field-value-input {
    font-size: 12px;
    padding: 2px 4px;
    height: 22px;
    border: 1px solid var(--border-default);
    border-radius: 3px;
    width: 100%;
}

/* 修复子表单字段中表达式验证器的样式 */
.subform-item-fields .expression-validator {
    position: relative;
    width: 100%;
}

.subform-item-fields .expression-validator input,
.subform-item-fields .expression-validator textarea {
    width: 100%;
    font-size: 12px;
    padding: 2px 4px;
    height: 22px;
    border: 1px solid var(--border-default);
    border-radius: 3px;
    font-family: var(--font-mono);
    background-color: var(--bg-light);
}

.subform-item-fields .expression-validator textarea {
    height: 40px;
    resize: vertical;
}