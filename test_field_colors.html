<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字段类型颜色测试</title>
    <link rel="stylesheet" href="workflow_node/styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .field-demo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .field-demo-label {
            min-width: 120px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">字段类型颜色方案测试</div>
        
        <div class="field-demo">
            <span class="field-demo-label">文本类型：</span>
            <span class="field-type field-type-string">字符</span>
            <span class="field-type field-type-text">字符</span>
            <span class="field-type field-type-textarea">字符</span>
        </div>
        
        <div class="field-demo">
            <span class="field-demo-label">数字类型：</span>
            <span class="field-type field-type-number">数字</span>
            <span class="field-type field-type-integer">整数</span>
            <span class="field-type field-type-float">小数</span>
        </div>
        
        <div class="field-demo">
            <span class="field-demo-label">布尔类型：</span>
            <span class="field-type field-type-boolean">布尔</span>
            <span class="field-type field-type-checkbox">布尔</span>
        </div>
        
        <div class="field-demo">
            <span class="field-demo-label">日期时间类型：</span>
            <span class="field-type field-type-date">日期</span>
            <span class="field-type field-type-time">时间</span>
            <span class="field-type field-type-datetime">日期时间</span>
        </div>
        
        <div class="field-demo">
            <span class="field-demo-label">特殊验证类型：</span>
            <span class="field-type field-type-email">邮箱</span>
            <span class="field-type field-type-url">URL</span>
            <span class="field-type field-type-phone">手机号</span>
            <span class="field-type field-type-idcard">身份证号</span>
        </div>
        
        <div class="field-demo">
            <span class="field-demo-label">选择类型：</span>
            <span class="field-type field-type-select">字符</span>
            <span class="field-type field-type-radio">字符</span>
        </div>
        
        <div class="field-demo">
            <span class="field-demo-label">复合类型：</span>
            <span class="field-type field-type-array">子表单</span>
            <span class="field-type field-type-object">对象</span>
            <span class="field-type field-type-related">关联表单</span>
        </div>
        
        <div class="field-demo">
            <span class="field-demo-label">默认类型：</span>
            <span class="field-type">未知类型</span>
        </div>
    </div>
    
    <div class="test-container">
        <div class="test-title">颜色说明</div>
        <ul>
            <li><strong>蓝色 (#3498db)</strong>：文本类型 - string, text, textarea</li>
            <li><strong>绿色 (#27ae60)</strong>：数字类型 - number, integer, float</li>
            <li><strong>紫色 (#9b59b6)</strong>：布尔类型 - boolean, checkbox</li>
            <li><strong>橙色 (#f39c12)</strong>：日期时间类型 - date, time, datetime</li>
            <li><strong>青色 (#17a2b8)</strong>：特殊验证类型 - email, url, phone, idcard</li>
            <li><strong>深蓝色 (#34495e)</strong>：选择类型 - select, radio</li>
            <li><strong>红色 (#e74c3c)</strong>：复合类型 - array, object, related</li>
            <li><strong>灰色 (#6c757d)</strong>：默认类型</li>
        </ul>
    </div>
</body>
</html>
