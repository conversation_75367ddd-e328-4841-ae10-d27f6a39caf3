# 表单异步验证规则

## 安装

```bash
npm i async-validator
```

## 基本使用

```js
import Schema from 'async-validator';

const descriptor = {
  name: {
    type: 'string',
    required: true,
  },
  age: {
    type: 'number',
    required: true,
    min: 18,
  },
};

const validator = new Schema(descriptor);

// 回调方式
validator.validate({ name: 'test', age: 20 }, (errors, fields) => {
  if (errors) {
    console.log('验证失败', errors);
    return;
  }
  console.log('验证通过');
});

// Promise方式
validator.validate({ name: 'test', age: 16 })
  .then(() => {
    console.log('验证通过');
  })
  .catch(({ errors, fields }) => {
    console.log('验证失败', errors);
  });
```

## API

### validate(source, [options], callback)

* `source`: 待验证的对象（必需）
* `options`: 验证选项（可选）
* `callback`: 验证完成的回调函数（可选）

### 验证选项

* `first`: 布尔值，遇到第一个错误时停止验证
* `firstFields`: 布尔值或字符串子表单，指定字段遇到第一个错误时停止验证

## 验证规则

### 基本类型

* `string`: 字符串类型
* `number`: 数字类型  
* `boolean`: 布尔类型
* `array`: 子表单类型
* `object`: 对象类型
* `email`: 邮箱格式
* `url`: URL格式

### 常用属性

* `required`: 必需字段
* `min`/`max`: 最小/最大值（数字）或长度（字符串/子表单）
* `len`: 精确长度
* `pattern`: 正则表达式
* `enum`: 枚举值
* `message`: 自定义错误信息

### 示例

```js
const descriptor = {
  "candidateName": [
    {
      "required": true,
      "message": "请输入候选人名",
      "trigger": "blur"
    },
    {
      "min": 2,
      "max": 50,
      "message": "长度在 2 到 50 个字符",
      "trigger": "blur"
    }
  ],
  "email": [
    {
      "required": true,
      "message": "请输入邮箱",
      "trigger": "blur"
    },
    {
      "type": "email",
      "message": "请输入有效的邮箱地址",
      "trigger": "blur"
    }
  ],
  "phoneNumber": [
    {
      "required": true,
      "message": "请输入电话号码",
      "trigger": "blur"
    },
    {
      "pattern": "^1[3-9]\\d{9}$",
      "message": "请输入有效的电话号码",
      "trigger": "blur"
    }
  ],
  "address": [
    {
      "required": true,
      "message": "请输入家庭地址",
      "trigger": "blur"
    },
    {
      "min": 2,
      "max": 200,
      "message": "长度在 2 到 200 个字符",
      "trigger": "blur"
    }
  ],
  "objective": [
    {
      "required": true,
      "message": "请输入求职意向",
      "trigger": "blur"
    },
    {
      "min": 2,
      "max": 100,
      "message": "长度在 2 到 100 个字符",
      "trigger": "blur"
    }
  ],
  "workList": {
    "rules": [
      {
        "required": true,
        "message": "请至少添加一个工作经历",
        "trigger": "change"
      }
    ],
    "childRules": {
      "companyName": [
        {
          "required": true,
          "message": "请输入公司名称",
          "trigger": "blur"
        },
        {
          "min": 2,
          "max": 100,
          "message": "长度在 2 到 100 个字符",
          "trigger": "blur"
        }
      ],
      "position": [
        {
          "required": true,
          "message": "请选择职位",
          "trigger": "change"
        }
      ],
      "startDate": [
        {
          "required": true,
          "message": "请选择开始日期",
          "trigger": "change"
        }
      ],
      "endDate": [
        {
          "required": true,
          "message": "请选择结束日期",
          "trigger": "change"
        }
      ]
    }
  },
  "tagList": [
    {
      "required": true,
      "message": "请至少添加一个标签",
      "trigger": "change"
    }
  ]
}
```

### 自定义验证

```js
const descriptor = {
  name: {
    validator(rule, value, callback) {
      if (value === 'admin') {
        callback(new Error('用户名不能是admin'));
      } else {
        callback();
      }
    }
  },
  asyncField: {
    asyncValidator(rule, value) {
      return new Promise((resolve, reject) => {
        // 异步验证逻辑
        setTimeout(() => {
          if (value === 'invalid') {
            reject(new Error('异步验证失败'));
          } else {
            resolve();
          }
        }, 1000);
      });
    }
  }
};
```