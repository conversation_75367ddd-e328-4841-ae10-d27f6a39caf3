/**
 * 表单验证规则配置器 - 核心数据管理
 * 负责数据存储、导入导出、初始化等核心功能
 */

// 全局变量
let configData = {};
let currentEditingField = null;
let currentEditingRule = null;

// 验证类型到中文的映射
const ruleTypeLabels = {
    'required': '必填',
    'type': '类型',
    'min': '最小值/长度',
    'max': '最大值/长度',
    'len': '精确长度',
    'pattern': '正则匹配',
    'enum': '枚举值',
    'custom': '自定义'
};

// 数据类型到中文的映射
const dataTypeLabels = {
    'string': '字符串',
    'number': '数字',
    'boolean': '布尔',
    'array': '子表单',
    'object': '对象',
    'email': '邮箱',
    'url': 'URL'
};

// 字段类型到中文的映射
const fieldTypeLabels = {
    // 基本类型
    'string': '字符串',
    'integer': '整数',
    'float': '小数',
    'boolean': '布尔值',
    // 日期时间类型
    'date': '日期',
    'time': '时间',
    'datetime': '日期时间',
    // 特殊类型
    'email': '邮箱',
    'url': 'URL',
    'phone': '手机号',
    'idcard': '身份证号',
    // 列表类型
    'list': '列表'
};

// 触发方式到中文的映射
const triggerLabels = {
    'blur': '失焦',
    'change': '改变',
    'submit': '提交'
};

/**
 * 初始化函数
 */
function init() {
    loadExample(); // 自动加载示例数据
}

/**
 * 更新JSON预览
 */
function updatePreview() {
    const output = document.getElementById('jsonOutput');
    if (Object.keys(configData).length === 0) {
        output.textContent = '// 配置的验证规则将在这里显示';
    } else {
        output.textContent = JSON.stringify(configData, null, 2);
    }
}

/**
 * 导出JSON配置到文件
 */
function exportJson() {
    const dataStr = JSON.stringify(configData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'form-validation-config.json';
    a.click();
    URL.revokeObjectURL(url);
}

/**
 * 显示导入JSON对话框
 */
function importJson() {
    document.getElementById('importModal').style.display = 'block';
}

/**
 * 执行JSON导入
 */
function doImportJson() {
    try {
        const jsonStr = document.getElementById('importJson').value;
        const importedData = JSON.parse(jsonStr);
        configData = importedData;
        updateDisplay();
        updatePreview();
        closeImportModal();
        alert('导入成功！');
    } catch (error) {
        alert('JSON 格式错误: ' + error.message);
    }
}

/**
 * 关闭导入对话框
 */
function closeImportModal() {
    document.getElementById('importModal').style.display = 'none';
}

/**
 * 加载示例数据
 */
function loadExample() {
    configData = {
        "候选人姓名": {
            "type": "string",
            "rules": [
                {
                    "required": true,
                    "message": "请输入候选人名",
                    "trigger": "blur"
                },
                {
                    "min": 2,
                    "max": 50,
                    "message": "长度在 2 到 50 个字符",
                    "trigger": "blur"
                }
            ]
        },
        "邮箱": {
            "type": "email",
            "rules": [
                {
                    "required": true,
                    "message": "请输入邮箱",
                    "trigger": "blur"
                },
                {
                    "type": "email",
                    "message": "请输入有效的邮箱地址",
                    "trigger": "blur"
                }
            ]
        },
        "电话号码": {
            "type": "phone",
            "rules": [
                {
                    "required": true,
                    "message": "请输入电话号码",
                    "trigger": "blur"
                },
                {
                    "pattern": "^1[3-9]\\d{9}$",
                    "message": "请输入有效的电话号码",
                    "trigger": "blur"
                }
            ]
        },
        "家庭地址": {
            "type": "string",
            "rules": [
                {
                    "required": true,
                    "message": "请输入家庭地址",
                    "trigger": "blur"
                },
                {
                    "min": 2,
                    "max": 200,
                    "message": "长度在 2 到 200 个字符",
                    "trigger": "blur"
                }
            ]
        },
        "求职意向": {
            "type": "string",
            "rules": [
                {
                    "required": true,
                    "message": "请输入求职意向",
                    "trigger": "blur"
                },
                {
                    "min": 2,
                    "max": 100,
                    "message": "长度在 2 到 100 个字符",
                    "trigger": "blur"
                }
            ]
        },
        "工作列表": {
            "type": "list",
            "elementType": "string",
            "rules": [
                {
                    "required": true,
                    "message": "请至少添加一个工作经历",
                    "trigger": "change"
                }
            ],
            "elementRules": [],
            "childRules": {
                "公司名称": {
                    "type": "string",
                    "rules": [
                        {
                            "required": true,
                            "message": "请输入公司名称",
                            "trigger": "blur"
                        },
                        {
                            "min": 2,
                            "max": 100,
                            "message": "长度在 2 到 100 个字符",
                            "trigger": "blur"
                        }
                    ]
                },
                "职位": {
                    "type": "string",
                    "rules": [
                        {
                            "required": true,
                            "message": "请选择职位",
                            "trigger": "change"
                        }
                    ]
                },
                "开始日期": {
                    "type": "date",
                    "rules": [
                        {
                            "required": true,
                            "message": "请选择开始日期",
                            "trigger": "change"
                        }
                    ]
                },
                "结束日期": {
                    "type": "date",
                    "rules": [
                        {
                            "required": true,
                            "message": "请选择结束日期",
                            "trigger": "change"
                        }
                    ]
                }
            }
        }
    };
    updateDisplay();
    updatePreview();
}

// 页面加载完成后初始化
window.onload = init;