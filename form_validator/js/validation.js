/**
 * 表单验证规则配置器 - 验证规则相关功能
 * 负责验证规则的管理、表单提交处理等功能
 */

/**
 * 根据字段类型更新验证规则选项
 */
function updateRuleOptionsByFieldType(fieldType) {
    const ruleTypeSelect = document.getElementById('ruleType');
    const originalOptions = ruleTypeSelect.innerHTML;

    // 保存所有选项
    if (!window.allRuleOptions) {
        window.allRuleOptions = originalOptions;
    }

    // 根据字段类型设置默认选项
    let defaultOptions = [];

    switch (fieldType) {
        case 'string':
            defaultOptions = ['required', 'min', 'max', 'len', 'pattern', 'enum'];
            break;
        case 'integer':
        case 'float':
            defaultOptions = ['required', 'type', 'min', 'max', 'enum'];
            break;
        case 'date':
        case 'time':
        case 'datetime':
            defaultOptions = ['required', 'type', 'min', 'max'];
            break;
        case 'boolean':
            defaultOptions = ['required', 'type'];
            break;
        case 'email':
        case 'url':
        case 'phone':
        case 'idcard':
            defaultOptions = ['required', 'type'];
            break;
        case 'list':
            defaultOptions = ['required', 'min', 'max'];
            break;
        default:
            // 默认显示所有选项
            defaultOptions = ['required', 'type', 'min', 'max', 'len', 'pattern', 'enum', 'custom'];
    }

    // 重新构建选项
    ruleTypeSelect.innerHTML = '';
    const allOptions = window.allRuleOptions.match(/<option value="([^"]+)">([^<]+)<\/option>/g);

    if (allOptions) {
        allOptions.forEach(option => {
            const match = option.match(/<option value="([^"]+)">([^<]+)<\/option>/);
            if (match) {
                const value = match[1];
                const text = match[2];
                if (defaultOptions.includes(value)) {
                    const optionElement = document.createElement('option');
                    optionElement.value = value;
                    optionElement.textContent = text;
                    ruleTypeSelect.appendChild(optionElement);
                }
            }
        });
    }
}

/**
 * 根据字段类型智能推荐验证规则
 */
function recommendRulesByFieldType(fieldType) {
    const ruleMessage = document.getElementById('ruleMessage');

    // 根据字段类型设置默认的错误消息
    switch (fieldType) {
        case 'string':
            ruleMessage.value = '请输入有效的字符串';
            break;
        case 'integer':
            ruleMessage.value = '请输入有效的整数';
            break;
        case 'float':
            ruleMessage.value = '请输入有效的小数';
            break;
        case 'date':
            ruleMessage.value = '请选择有效的日期';
            break;
        case 'time':
            ruleMessage.value = '请选择有效的时间';
            break;
        case 'datetime':
            ruleMessage.value = '请选择有效的日期时间';
            break;
        case 'boolean':
            ruleMessage.value = '请选择有效的布尔值';
            break;
        case 'email':
            ruleMessage.value = '请输入有效的邮箱地址';
            break;
        case 'url':
            ruleMessage.value = '请输入有效的URL';
            break;
        case 'phone':
            ruleMessage.value = '请输入有效的手机号';
            break;
        case 'idcard':
            ruleMessage.value = '请输入有效的身份证号';
            break;
        case 'list':
            ruleMessage.value = '请选择有效的列表项';
            break;
        default:
            ruleMessage.value = '请输入有效的值';
    }
}

/**
 * 规则类型变化事件处理
 */
function onRuleTypeChange() {
    const ruleType = document.getElementById('ruleType').value;
    const dataTypeGroup = document.getElementById('dataTypeGroup');
    const valueGroup = document.getElementById('valueGroup');
    const patternGroup = document.getElementById('patternGroup');
    const enumGroup = document.getElementById('enumGroup');
    const customGroup = document.getElementById('customGroup');
    const valueLabel = document.getElementById('valueLabel');
    const ruleValue = document.getElementById('ruleValue');

    // 隐藏所有组
    dataTypeGroup.style.display = 'none';
    valueGroup.style.display = 'none';
    patternGroup.style.display = 'none';
    enumGroup.style.display = 'none';
    customGroup.style.display = 'none';

    // 获取字段类型
    let fieldType = '';
    if (currentEditingField) {
        // 检查是否是子字段
        const isChildRule = Object.values(configData).some(field =>
            field.childRules && Object.keys(field.childRules).includes(currentEditingField)
        );

        if (isChildRule) {
            // 找到父字段
            let parentField = null;
            Object.keys(configData).forEach(key => {
                if (configData[key].childRules && configData[key].childRules[currentEditingField]) {
                    parentField = configData[key];
                }
            });

            if (parentField && parentField.type === 'list') {
                fieldType = parentField.elementType || 'string';
            }
        } else {
            // 普通字段类型
            const field = configData[currentEditingField];
            if (field && field.type) {
                fieldType = field.type;
            }
        }
    }

    // 根据规则类型显示对应的输入组
    switch (ruleType) {
        case 'type':
            dataTypeGroup.style.display = 'block';
            break;
        case 'min':
            valueGroup.style.display = 'block';
            // 根据字段类型调整输入框类型和标签
            if (fieldType === 'integer' || fieldType === 'float') {
                valueLabel.textContent = '最小值';
                ruleValue.type = 'number';
                if (fieldType === 'integer') {
                    ruleValue.step = '1';
                } else {
                    ruleValue.step = '0.01';
                }
            } else if (fieldType === 'date' || fieldType === 'time' || fieldType === 'datetime') {
                valueLabel.textContent = '最小值';
                ruleValue.type = fieldType === 'time' ? 'time' : 'date';
                if (fieldType === 'datetime') {
                    ruleValue.type = 'datetime-local';
                }
            } else {
                valueLabel.textContent = '最小长度';
                ruleValue.type = 'number';
                ruleValue.step = '1';
            }
            break;
        case 'max':
            valueGroup.style.display = 'block';
            // 根据字段类型调整输入框类型和标签
            if (fieldType === 'integer' || fieldType === 'float') {
                valueLabel.textContent = '最大值';
                ruleValue.type = 'number';
                if (fieldType === 'integer') {
                    ruleValue.step = '1';
                } else {
                    ruleValue.step = '0.01';
                }
            } else if (fieldType === 'date' || fieldType === 'time' || fieldType === 'datetime') {
                valueLabel.textContent = '最大值';
                ruleValue.type = fieldType === 'time' ? 'time' : 'date';
                if (fieldType === 'datetime') {
                    ruleValue.type = 'datetime-local';
                }
            } else {
                valueLabel.textContent = '最大长度';
                ruleValue.type = 'number';
                ruleValue.step = '1';
            }
            break;
        case 'len':
            valueGroup.style.display = 'block';
            valueLabel.textContent = '精确长度';
            ruleValue.type = 'number';
            ruleValue.step = '1';
            break;
        case 'pattern':
            patternGroup.style.display = 'block';
            break;
        case 'enum':
            enumGroup.style.display = 'block';
            break;
        case 'custom':
            customGroup.style.display = 'block';
            break;
    }
}

/**
 * 初始化表单提交处理
 */
function initFormHandlers() {
    // 字段表单提交处理
    document.getElementById('fieldForm').onsubmit = function (e) {
        e.preventDefault();

        const fieldName = document.getElementById('fieldName').value;
        const fieldType = document.getElementById('fieldType').value;

        if (currentEditingField && currentEditingField !== fieldName) {
            delete configData[currentEditingField];
        }

        // 根据新的字段类型结构创建配置
        if (fieldType === 'list') {
            // 列表类型
            const listElementType = document.getElementById('listElementType').value;
            configData[fieldName] = {
                type: 'list',
                elementType: listElementType,
                rules: [],
                elementRules: [],
                childRules: {}
            };
        } else {
            // 基本类型、日期时间类型和特殊类型
            configData[fieldName] = {
                type: fieldType,
                rules: []
            };
        }

        closeModal();
        updateDisplay();
        updatePreview();
    };

    // 规则表单提交处理
    document.getElementById('ruleForm').onsubmit = function (e) {
        e.preventDefault();

        const ruleType = document.getElementById('ruleType').value;
        const trigger = document.getElementById('ruleTrigger').value;
        const message = document.getElementById('ruleMessage').value;

        // 获取字段类型
        let fieldType = '';
        if (currentEditingField) {
            // 检查是否是子字段
            const isChildRule = Object.values(configData).some(field =>
                field.childRules && Object.keys(field.childRules).includes(currentEditingField)
            );

            if (isChildRule) {
                // 找到父字段
                let parentField = null;
                Object.keys(configData).forEach(key => {
                    if (configData[key].childRules && configData[key].childRules[currentEditingField]) {
                        parentField = configData[key];
                    }
                });

                if (parentField && parentField.type === 'list') {
                    fieldType = parentField.elementType || 'string';
                }
            } else {
                // 普通字段类型
                const field = configData[currentEditingField];
                if (field && field.type) {
                    fieldType = field.type;
                }
            }
        }

        const rule = {
            trigger: trigger,
            message: message
        };

        // 根据规则类型设置规则属性
        switch (ruleType) {
            case 'required':
                rule.required = true;
                break;
            case 'type':
                rule.type = document.getElementById('dataType').value;
                break;
            case 'min':
                const minValue = document.getElementById('ruleValue').value;
                // 根据字段类型转换输入值
                if (fieldType === 'integer') {
                    rule.min = parseInt(minValue);
                } else if (fieldType === 'float') {
                    rule.min = parseFloat(minValue);
                } else if (fieldType === 'date' || fieldType === 'time' || fieldType === 'datetime') {
                    rule.min = minValue; // 日期时间值保持原样
                } else {
                    rule.min = isNaN(parseInt(minValue)) ? minValue : parseInt(minValue);
                }
                break;
            case 'max':
                const maxValue = document.getElementById('ruleValue').value;
                // 根据字段类型转换输入值
                if (fieldType === 'integer') {
                    rule.max = parseInt(maxValue);
                } else if (fieldType === 'float') {
                    rule.max = parseFloat(maxValue);
                } else if (fieldType === 'date' || fieldType === 'time' || fieldType === 'datetime') {
                    rule.max = maxValue; // 日期时间值保持原样
                } else {
                    rule.max = isNaN(parseInt(maxValue)) ? maxValue : parseInt(maxValue);
                }
                break;
            case 'len':
                rule.len = parseInt(document.getElementById('ruleValue').value);
                break;
            case 'pattern':
                rule.pattern = document.getElementById('rulePattern').value;
                break;
            case 'enum':
                const enumValues = document.getElementById('ruleEnum').value.split(',').map(v => v.trim());
                rule.enum = enumValues;
                break;
            case 'custom':
                try {
                    rule.validator = eval('(' + document.getElementById('ruleCustom').value + ')');
                } catch (error) {
                    alert('自定义函数格式错误: ' + error.message);
                    return;
                }
                break;
        }

        // 添加或更新规则
        let targetRules;

        // 检查是否是子字段规则
        const modalTitle = document.getElementById('ruleModalTitle').textContent;
        const isChildRule = modalTitle.includes('子字段') || Object.values(configData).some(field =>
            field.childRules && Object.keys(field.childRules).includes(currentEditingField)
        );

        if (isChildRule) {
            // 找到父字段
            let parentField = null;
            Object.keys(configData).forEach(key => {
                if (configData[key].childRules && configData[key].childRules[currentEditingField]) {
                    parentField = key;
                }
            });

            if (parentField) {
                targetRules = configData[parentField].childRules[currentEditingField].rules;
            }
        } else {
            targetRules = configData[currentEditingField].rules;
        }

        if (targetRules) {
            if (currentEditingRule !== null) {
                targetRules[currentEditingRule] = rule;
            } else {
                targetRules.push(rule);
            }
        }

        closeRuleModal();
        updateDisplay();
        updatePreview();
    };
}

// 当页面加载完成后初始化表单处理器
document.addEventListener('DOMContentLoaded', function() {
    initFormHandlers();
});