/**
 * 表单验证规则配置器 - UI操作和显示逻辑
 * 负责界面交互、模态框管理、表格显示等UI相关功能
 */

/**
 * 字段类型变化事件处理
 */
function onFieldTypeChange() {
    const fieldType = document.getElementById('fieldType').value;
    const listElementTypeGroup = document.getElementById('listElementTypeGroup');

    if (fieldType === 'list') {
        listElementTypeGroup.style.display = 'block';
    } else {
        listElementTypeGroup.style.display = 'none';
    }
}

/**
 * 添加字段
 */
function addField() {
    currentEditingField = null;
    document.getElementById('modalTitle').textContent = '添加字段';
    document.getElementById('fieldForm').reset();
    document.getElementById('fieldModal').style.display = 'block';
}

/**
 * 编辑字段
 */
function editField(fieldName) {
    currentEditingField = fieldName;
    document.getElementById('modalTitle').textContent = '编辑字段';
    document.getElementById('fieldName').value = fieldName;

    const field = configData[fieldName];

    // 检查字段类型结构
    if (field && field.type === 'list') {
        // 新版列表类型
        document.getElementById('fieldType').value = 'list';
        document.getElementById('listElementType').value = field.elementType || 'string';
        // 触发字段类型变化事件，显示列表元素类型选择器
        onFieldTypeChange();
    } else if (field && field.type && field.type !== 'list') {
        // 新版基本类型、日期时间类型和特殊类型
        document.getElementById('fieldType').value = field.type;
        // 隐藏列表元素类型选择器
        document.getElementById('listElementTypeGroup').style.display = 'none';
    } else {
        // 未定义类型，默认为字符串
        document.getElementById('fieldType').value = 'string';
        // 隐藏列表元素类型选择器
        document.getElementById('listElementTypeGroup').style.display = 'none';
    }

    document.getElementById('fieldModal').style.display = 'block';
}

/**
 * 删除字段
 */
function deleteField(fieldName) {
    if (confirm(`确定要删除字段 "${fieldName}" 吗？`)) {
        delete configData[fieldName];
        updateDisplay();
        updatePreview();
    }
}

/**
 * 关闭字段模态框
 */
function closeModal() {
    document.getElementById('fieldModal').style.display = 'none';
}

/**
 * 添加规则
 */
function addRule(fieldName, isChild = false, parentName = null) {
    currentEditingField = fieldName;
    currentEditingRule = null;
    document.getElementById('ruleModalTitle').textContent = `为 "${fieldName}" 添加验证规则`;
    document.getElementById('ruleForm').reset();

    // 获取字段类型
    let fieldType = '';
    if (isChild && parentName) {
        // 子字段类型
        const parentField = configData[parentName];
        if (parentField && parentField.type === 'list') {
            fieldType = parentField.elementType || 'string';
        }
    } else {
        // 普通字段类型
        const field = configData[fieldName];
        if (field && field.type) {
            fieldType = field.type;
        }
    }

    // 根据字段类型动态调整验证规则选项
    updateRuleOptionsByFieldType(fieldType);

    // 智能推荐验证规则
    recommendRulesByFieldType(fieldType);

    document.getElementById('ruleModal').style.display = 'block';
    onRuleTypeChange();
}

/**
 * 编辑规则
 */
function editRule(fieldName, ruleIndex, isChild = false, parentName = null) {
    currentEditingField = fieldName;
    currentEditingRule = ruleIndex;

    let rules;
    if (isChild) {
        rules = configData[parentName].childRules[fieldName].rules;
    } else {
        rules = configData[fieldName].rules;
    }

    const rule = rules[ruleIndex];
    if (!rule) return;

    document.getElementById('ruleModalTitle').textContent = `编辑 "${fieldName}" 的验证规则`;

    // 获取字段类型
    let fieldType = '';
    if (isChild && parentName) {
        // 子字段类型
        const parentField = configData[parentName];
        if (parentField && parentField.type === 'list') {
            fieldType = parentField.elementType || 'string';
        }
    } else {
        // 普通字段类型
        const field = configData[fieldName];
        if (field && field.type) {
            fieldType = field.type;
        }
    }

    // 根据字段类型动态调整验证规则选项
    updateRuleOptionsByFieldType(fieldType);

    // 根据规则内容推断验证类型
    if (rule.required !== undefined) {
        document.getElementById('ruleType').value = 'required';
    } else if (rule.type !== undefined) {
        document.getElementById('ruleType').value = 'type';
        document.getElementById('dataType').value = rule.type;
    } else if (rule.min !== undefined) {
        document.getElementById('ruleType').value = 'min';
        document.getElementById('ruleValue').value = rule.min;
    } else if (rule.max !== undefined) {
        document.getElementById('ruleType').value = 'max';
        document.getElementById('ruleValue').value = rule.max;
    } else if (rule.len !== undefined) {
        document.getElementById('ruleType').value = 'len';
        document.getElementById('ruleValue').value = rule.len;
    } else if (rule.pattern !== undefined) {
        document.getElementById('ruleType').value = 'pattern';
        document.getElementById('rulePattern').value = rule.pattern;
    } else if (rule.enum !== undefined) {
        document.getElementById('ruleType').value = 'enum';
        document.getElementById('ruleEnum').value = rule.enum.join(',');
    } else if (rule.validator !== undefined) {
        document.getElementById('ruleType').value = 'custom';
        document.getElementById('ruleCustom').value = rule.validator.toString();
    }

    document.getElementById('ruleTrigger').value = rule.trigger || 'blur';
    document.getElementById('ruleMessage').value = rule.message || '';

    onRuleTypeChange();
    document.getElementById('ruleModal').style.display = 'block';
}

/**
 * 删除规则
 */
function deleteRule(fieldName, ruleIndex, isChild = false, parentName = null) {
    if (confirm('确定要删除这个验证规则吗？')) {
        let rules;
        if (isChild) {
            rules = configData[parentName].childRules[fieldName].rules;
        } else {
            rules = configData[fieldName].rules;
        }

        rules.splice(ruleIndex, 1);

        // 如果没有规则了，删除字段
        if (rules.length === 0) {
            if (isChild) {
                delete configData[parentName].childRules[fieldName];
            } else {
                delete configData[fieldName];
            }
        }

        updateDisplay();
        updatePreview();
    }
}

/**
 * 添加子字段
 */
function addChildField(parentName) {
    const childName = prompt('请输入子字段名称:');
    if (!childName) return;

    if (!configData[parentName].childRules) {
        configData[parentName].childRules = {};
    }

    // 根据父字段的元素类型创建子字段
    const parentField = configData[parentName];
    const elementType = parentField.elementType || 'string';
    
    configData[parentName].childRules[childName] = {
        type: elementType,
        rules: []
    };
    addRule(childName, true, parentName);
}

/**
 * 关闭规则模态框
 */
function closeRuleModal() {
    document.getElementById('ruleModal').style.display = 'none';
}

/**
 * 更新显示
 */
function updateDisplay() {
    const grid = document.getElementById('treeGrid');
    const headerRow = grid.querySelector('.tree-row');
    grid.innerHTML = '';
    grid.appendChild(headerRow);

    Object.keys(configData).forEach(fieldName => {
        const field = configData[fieldName];

        // 创建字段行
        const fieldRow = createFieldRow(fieldName, field);
        grid.appendChild(fieldRow);

        // 创建规则行 - 支持新的字段类型结构
        let rules = [];
        if (field && field.type && field.rules) {
            // 新版字段类型结构
            rules = field.rules;
        }

        if (Array.isArray(rules)) {
            rules.forEach((rule, index) => {
                const ruleRow = createRuleRow(fieldName, rule, index);
                grid.appendChild(ruleRow);
            });
        }

        // 处理子字段 - 支持新的字段类型结构
        if (field && field.childRules) {
            Object.keys(field.childRules).forEach(childName => {
                const childField = field.childRules[childName];
                const childFieldRow = createFieldRow(childName, childField, true, fieldName);
                grid.appendChild(childFieldRow);

                // 创建子字段的规则行
                if (childField.rules && Array.isArray(childField.rules)) {
                    childField.rules.forEach((rule, index) => {
                        const childRuleRow = createRuleRow(childName, rule, index, true, fieldName);
                        grid.appendChild(childRuleRow);
                    });
                }
            });
        }

        // 处理元素规则 - 新版列表类型的元素规则
        if (field && field.type === 'list' && field.elementRules && Array.isArray(field.elementRules)) {
            field.elementRules.forEach((rule, index) => {
                const elementRuleRow = createRuleRow(fieldName + '元素', rule, index, false, null);
                grid.appendChild(elementRuleRow);
            });
        }
    });
}

/**
 * 创建字段行
 */
function createFieldRow(fieldName, field, isChild = false, parentName = null) {
    const row = document.createElement('div');
    row.className = `tree-row ${isChild ? 'child' : 'parent'}`;
    row.style.background = isChild ? '#f1f2f6' : '#e9ecef';
    row.style.fontWeight = 'bold';

    let fieldTypeDisplay = '';
    let ruleCount = 0;

    // 判断字段类型结构
    if (field && field.type === 'list') {
        // 新版列表类型
        fieldTypeDisplay = fieldTypeLabels.list;
        ruleCount = field.rules ? field.rules.length : 0;
    } else if (field && field.type) {
        // 新版基本类型、日期时间类型和特殊类型
        fieldTypeDisplay = fieldTypeLabels[field.type] || field.type;
        ruleCount = field.rules ? field.rules.length : 0;
    } else {
        // 默认情况
        fieldTypeDisplay = '未知类型';
        ruleCount = 0;
    }

    row.innerHTML = `
        <div></div>
        <div class="field-name">${fieldName}</div>
        <div>${fieldTypeDisplay}</div>
        <div>${ruleCount} 条规则</div>
        <div>-</div>
        <div class="action-btns">
            <button class="btn btn-sm btn-primary" onclick="editField('${fieldName}')">编辑</button>
            <button class="btn btn-sm btn-success" onclick="addRule('${fieldName}', ${isChild}, '${parentName || ''}')">+规则</button>
            ${!isChild && field.type === 'list' ? `<button class="btn btn-sm btn-success" onclick="addChildField('${fieldName}')">+子字段</button>` : ''}
            <button class="btn btn-sm btn-danger" onclick="deleteField('${fieldName}')">删除</button>
        </div>
    `;

    return row;
}

/**
 * 创建规则行
 */
function createRuleRow(fieldName, rule, ruleIndex, isChild = false, parentName = null) {
    const row = document.createElement('div');
    row.className = 'tree-row';
    row.style.marginLeft = isChild ? '40px' : '20px';
    row.style.background = '#fdfdfd';
    row.style.borderLeft = '3px solid #3498db';

    // 分析规则类型
    let ruleTypeText = '';
    if (rule.required) ruleTypeText = '必填';
    else if (rule.type) ruleTypeText = `类型:${rule.type}`;
    else if (rule.min !== undefined) ruleTypeText = `最小:${rule.min}`;
    else if (rule.max !== undefined) ruleTypeText = `最大:${rule.max}`;
    else if (rule.len !== undefined) ruleTypeText = `长度:${rule.len}`;
    else if (rule.pattern) ruleTypeText = '正则匹配';
    else if (rule.enum) ruleTypeText = '枚举值';
    else if (rule.validator) ruleTypeText = '自定义';

    row.innerHTML = `
        <div></div>
        <div style="padding-left: 20px; color: #666;">→ 规则${ruleIndex + 1}</div>
        <div>${ruleTypeText}</div>
        <div style="font-size: 12px; color: #666;">${rule.message || '-'}</div>
        <div>${triggerLabels[rule.trigger] || rule.trigger}</div>
        <div class="action-btns">
            <button class="btn btn-sm btn-primary" onclick="editRule('${fieldName}', ${ruleIndex}, ${isChild}, '${parentName || ''}')">编辑</button>
            <button class="btn btn-sm btn-danger" onclick="deleteRule('${fieldName}', ${ruleIndex}, ${isChild}, '${parentName || ''}')">删除</button>
        </div>
    `;

    return row;
}