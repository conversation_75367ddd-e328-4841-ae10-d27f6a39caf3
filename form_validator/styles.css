/* 表单验证规则配置器样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.controls {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover {
    background: #219a52;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 20px;
}

.tree-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tree-header {
    background: #34495e;
    color: white;
    padding: 15px;
    font-weight: 600;
}

.tree-grid {
    min-height: 600px;
}

.tree-row {
    display: grid;
    grid-template-columns: 30px 0.8fr 80px 120px 100px 250px;
    align-items: center;
    padding: 5px 5px;
    border-bottom: 1px solid #ecf0f1;
    transition: background 0.2s;
    font-size: 12px;
}

.tree-row:hover {
    background: #f8f9fa;
}

.tree-row.parent {
    background: #ecf0f1;
    font-weight: 500;
}

.tree-row.child {
    margin-left: 20px;
    background: #f8f9fa;
}

.tree-row.child .field-name {
    padding-left: 20px;
}

.expand-btn {
    width: 20px;
    height: 20px;
    border: none;
    background: #3498db;
    color: white;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.expand-btn:disabled {
    background: transparent;
    cursor: default;
}

.field-name {
    font-weight: 500;
}

select,
input {
    padding: 4px 8px;
    border: 1px solid #bdc3c7;
    border-radius: 3px;
    font-size: 13px;
}

.checkbox {
    transform: scale(1.2);
}

.preview-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.preview-header {
    background: #27ae60;
    color: white;
    padding: 15px;
    font-weight: 600;
}

.preview-content {
    padding: 15px;
    flex: 1;
}

.json-output {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    overflow-x: auto;
    min-height: 400px;
}

.action-btns {
    display: flex;
    gap: 5px;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    background: white;
    margin: 50px auto;
    padding: 20px;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.close {
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
}

textarea {
    height: 200px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 12px;
}

@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
    }
}