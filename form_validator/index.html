<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单验证规则配置器</title>
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>表单验证规则配置器</h1>
            <p>可视化配置基于 async-validator 的表单验证规则</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="addField()">+ 添加字段</button>
            <button class="btn btn-primary" onclick="loadExample()">加载示例</button>
        </div>

        <div class="main-content">
            <div class="tree-container">
                <div class="tree-grid" id="treeGrid">
                    <div class="tree-row" style="background: #34495e; color: white; font-weight: bold;">
                        <div></div>
                        <div>名称</div>
                        <div>类型</div>
                        <div>验证规则</div>
                        <div>触发</div>
                        <div>操作</div>
                    </div>
                </div>
            </div>

            <div class="preview-container">
                <div class="preview-header">
                    JSON 预览
                </div>
                <div class="preview-content">
                    <div class="json-output" id="jsonOutput">
                        // 配置的验证规则将在这里显示
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑字段模态框 -->
    <div id="fieldModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加字段</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <form id="fieldForm">
                <div class="form-group">
                    <label>字段名称</label>
                    <input type="text" id="fieldName" required>
                </div>
                <div class="form-group">
                    <label>字段类型</label>
                    <select id="fieldType" onchange="onFieldTypeChange()">
                        <optgroup label="基本类型">
                            <option value="string">字符串</option>
                            <option value="integer">整数</option>
                            <option value="float">小数</option>
                            <option value="boolean">布尔值</option>
                        </optgroup>
                        <optgroup label="日期时间类型">
                            <option value="date">日期</option>
                            <option value="time">时间</option>
                            <option value="datetime">日期时间</option>
                        </optgroup>
                        <optgroup label="特殊类型">
                            <option value="email">邮箱</option>
                            <option value="url">URL</option>
                            <option value="phone">手机号</option>
                            <option value="idcard">身份证号</option>
                        </optgroup>
                        <optgroup label="列表类型">
                            <option value="list">列表（需要选择元素类型）</option>
                        </optgroup>
                    </select>
                </div>
                <div class="form-group" id="listElementTypeGroup" style="display: none;">
                    <label>列表元素类型</label>
                    <select id="listElementType">
                        <option value="string">字符串</option>
                        <option value="integer">整数</option>
                        <option value="float">小数</option>
                        <option value="boolean">布尔值</option>
                        <option value="date">日期</option>
                        <option value="time">时间</option>
                        <option value="datetime">日期时间</option>
                        <option value="email">邮箱</option>
                        <option value="url">URL</option>
                        <option value="phone">手机号</option>
                        <option value="idcard">身份证号</option>
                    </select>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="btn" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 添加/编辑规则模态框 -->
    <div id="ruleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="ruleModalTitle">添加验证规则</h3>
                <span class="close" onclick="closeRuleModal()">&times;</span>
            </div>
            <form id="ruleForm">
                <div class="form-group">
                    <label>验证类型</label>
                    <select id="ruleType" onchange="onRuleTypeChange()">
                        <option value="required">必填验证</option>
                        <option value="type">类型验证</option>
                        <option value="min">最小值/长度</option>
                        <option value="max">最大值/长度</option>
                        <option value="len">精确长度</option>
                        <option value="pattern">正则匹配</option>
                        <option value="enum">枚举值</option>
                        <option value="custom">自定义函数</option>
                    </select>
                </div>

                <div class="form-group" id="dataTypeGroup" style="display: none;">
                    <label>数据类型</label>
                    <select id="dataType">
                        <option value="string">字符串</option>
                        <option value="number">数字</option>
                        <option value="boolean">布尔</option>
                        <option value="array">子表单</option>
                        <option value="object">对象</option>
                        <option value="email">邮箱</option>
                        <option value="url">URL</option>
                    </select>
                </div>

                <div class="form-group" id="valueGroup" style="display: none;">
                    <label id="valueLabel">值</label>
                    <input type="text" id="ruleValue" placeholder="请输入值">
                </div>

                <div class="form-group" id="patternGroup" style="display: none;">
                    <label>正则表达式</label>
                    <input type="text" id="rulePattern" placeholder="例如: ^1[3-9]\\d{9}$">
                </div>

                <div class="form-group" id="enumGroup" style="display: none;">
                    <label>枚举值 (逗号分隔)</label>
                    <input type="text" id="ruleEnum" placeholder="例如: 选项1,选项2,选项3">
                </div>

                <div class="form-group" id="customGroup" style="display: none;">
                    <label>自定义验证函数</label>
                    <textarea id="ruleCustom" placeholder="function(rule, value, callback) { ... }"></textarea>
                </div>

                <div class="form-group">
                    <label>触发方式</label>
                    <select id="ruleTrigger">
                        <option value="blur">失去焦点 (blur)</option>
                        <option value="change">值改变 (change)</option>
                        <option value="submit">提交时 (submit)</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>错误信息</label>
                    <input type="text" id="ruleMessage" placeholder="自定义错误信息" required>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" class="btn" onclick="closeRuleModal()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>


    <!-- 引入JavaScript文件 -->
    <script src="js/main.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/validation.js"></script>
</body>

</html>